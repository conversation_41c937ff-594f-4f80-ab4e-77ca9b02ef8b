.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 6px;
  margin-top: -2px;
  background: #dc2626;
  cursor: pointer;
}

.volume-slider::-webkit-slider-runnable-track {
  width: 100%;
  height: 4px;
  cursor: pointer;
  border-radius: 2px;
  background: #4b5563;
}

/* Webkit浏览器的样式 */
input[type="range"].volume-slider::-webkit-slider-runnable-track {
  background: #4b5563;
}

/* Firefox浏览器的样式 */
input[type="range"].volume-slider::-moz-range-track {
  background: #4b5563;
  height: 4px;
  border: none;
  border-radius: 2px;
}

input[type="range"].volume-slider::-moz-range-progress {
  background: #dc2626;
  height: 4px;
  border-radius: 2px;
}

input[type="range"].volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 6px;
  cursor: pointer;
  background: #dc2626;
  margin-top: -4px;
}

input[type="range"].volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  background: #dc2626;
}
