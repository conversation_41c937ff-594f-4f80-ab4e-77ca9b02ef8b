"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HappyUsers from "./happy-users";
import { Hero as HeroType } from "@/types/blocks/hero";
import Icon from "@/components/icon";
import { <PERSON> } from "@/i18n/navigation";
import GradientButton from "@/components/ui/gradient-button";
import { useState } from "react";
import ShootingStars from "@/components/effects/shooting-stars";
import Typewriter from "@/components/effects/typewriter";
import { useAppContext } from "@/contexts/app";

export default function Hero({ hero }: { hero: HeroType }) {
  const { setShowSignModal } = useAppContext();

  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  return (
    <>
      {/* Make hero fill the viewport minus the sticky header height */}
      <section
        className="relative overflow-hidden flex items-center justify-center"
        style={{ minHeight: "calc(100vh - var(--header-height, 64px))" }}
      >
        {/* Background meteor effect */}
        <ShootingStars />
        <div className="container">
          {hero.show_badge && (
            <div className="flex items-center justify-center mb-8">
              <img
                src="/imgs/badges/phdaily.svg"
                alt="phdaily"
                className="h-10 object-cover"
              />
            </div>
          )}
          <div className="text-center">
            {hero.announcement && (
              <Link
                href={hero.announcement.url as any}
                className="mx-auto mb-3 inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm"
              >
                {hero.announcement.label && (
                  <Badge>{hero.announcement.label}</Badge>
                )}
                {hero.announcement.title}
              </Link>
            )}

            {/* Heading with optional highlight and inline typewriter */}
            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-3 mt-4 max-w-6xl text-balance min-h-[2lh] text-4xl font-bold lg:mb-7 lg:text-7xl">
                {texts[0]}
                <span className="bg-linear-to-r from-primary via-primary to-primary bg-clip-text text-transparent">
                  {highlightText}
                </span>
                {texts[1]}
                {Array.isArray(hero.typewriter_texts) &&
                  hero.typewriter_texts.length > 0 && (
                    <>
                      {" "}
                      <Typewriter texts={hero.typewriter_texts} />
                    </>
                  )}
              </h1>
            ) : (
              <h1 className="mx-auto mb-3 mt-4 max-w-6xl text-balance min-h-[2lh] text-4xl font-bold lg:mb-7 lg:text-7xl">
                {hero.title}
                {Array.isArray(hero.typewriter_texts) &&
                  hero.typewriter_texts.length > 0 && (
                    <>
                      {" "}
                      <Typewriter texts={hero.typewriter_texts} />
                    </>
                  )}
              </h1>
            )}

            <p
              className="m mx-auto max-w-3xl text-muted-foreground lg:text-xl"
              dangerouslySetInnerHTML={{ __html: hero.description || "" }}
            />

            {hero.prompt_area && (
              <section className="mx-auto mt-8 w-full max-w-[557px] px-[20px]">
                <div
                  className="relative flex w-full flex-row items-center rounded-[100px] border border-white/10 bg-foreground/5 backdrop-blur-[18.827px] p-[10px] md:p-[3.77px] md:pt-[3.77px] md:pl-0"
                  style={{
                    boxShadow:
                      "rgba(255, 176, 3, 0.2) -5px 0px 40px, rgba(254, 60, 125, 0.2) 5px 0px 40px",
                    animation: "heroFlash 3s ease-in-out infinite",
                  }}
                >
                  <div className="relative flex min-w-0 flex-1 flex-row">
                    <div className="absolute left-[8px] top-1/2 -translate-y-1/2 transform cursor-pointer md:left-[16px] text-white/20">
                      <img
                        src={
                          hero.prompt_area.left_icon || "/imgs/icons/spark.svg"
                        }
                        alt="spark"
                        className="h-5 w-5"
                      />
                    </div>
                    <PromptInput
                      placeholder={
                        hero.prompt_area.placeholder || "Type any idea you have"
                      }
                      defaultValue={
                        hero.prompt_area.default_value ||
                        "Intense rhythm song about transformations"
                      }
                    />
                  </div>
                  <div className="flex flex-shrink-0 justify-end md:block">
                    {(() => {
                      const btn = hero.prompt_area?.button;
                      const label =
                        btn?.title ||
                        hero.prompt_area?.button?.title ||
                        "Create";
                      const href = btn?.url || hero.prompt_area?.button?.url;
                      const target =
                        btn?.target ||
                        hero.prompt_area?.button?.target ||
                        "_self";
                      const iconSrc =
                        (btn as any)?.iconSrc ||
                        hero.prompt_area?.button?.iconSrc ||
                        "/imgs/icons/music.svg";
                      const gradient = btn?.gradient;

                      const content = gradient ? (
                        <GradientButton
                          iconSrc={iconSrc}
                          label={label}
                          from={
                            typeof gradient === "object" && gradient.from
                              ? gradient.from
                              : undefined
                          }
                          via={
                            typeof gradient === "object" && gradient.via
                              ? gradient.via
                              : undefined
                          }
                          to={
                            typeof gradient === "object" && gradient.to
                              ? gradient.to
                              : undefined
                          }
                          className={
                            typeof gradient === "object" && gradient.className
                              ? gradient.className
                              : undefined
                          }
                        />
                      ) : (
                        <Button size="lg" className="rounded-full px-6 h-10">
                          {btn?.icon ? (
                            <Icon name={btn.icon} />
                          ) : iconSrc ? (
                            <img
                              src={iconSrc}
                              alt="action"
                              className="h-5 w-6"
                            />
                          ) : null}
                          {label}
                        </Button>
                      );

                      return (
                        <div onClick={() => setShowSignModal(true)}>
                          {content}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              </section>
            )}
            {hero.buttons && (
              <div className="mt-8 flex flex-col justify-center gap-4 sm:flex-row">
                {hero.buttons.map((item, i) => {
                  return (
                    <Link
                      key={i}
                      href={item.url as any}
                      target={item.target || ""}
                      className="flex items-center"
                    >
                      <Button
                        className="w-full"
                        size="lg"
                        variant={item.variant || "default"}
                      >
                        {item.icon && <Icon name={item.icon} className="" />}
                        {item.title}
                      </Button>
                    </Link>
                  );
                })}
              </div>
            )}
            {hero.tip && (
              <p className="mt-8 text-md text-muted-foreground">{hero.tip}</p>
            )}
            {hero.show_happy_users && <HappyUsers />}
          </div>
        </div>
      </section>
    </>
  );
}

function PromptInput({
  placeholder,
  defaultValue,
}: {
  placeholder: string;
  defaultValue: string;
}) {
  const [value, setValue] = useState<string>(defaultValue);
  return (
    <input
      aria-label="Prompt"
      placeholder={placeholder}
      className="flex min-w-0 flex-1 truncate bg-transparent pr-[10px] pl-[47px] font-sans text-base shadow-none outline-none md:text-lg"
      type="text"
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  );
}
