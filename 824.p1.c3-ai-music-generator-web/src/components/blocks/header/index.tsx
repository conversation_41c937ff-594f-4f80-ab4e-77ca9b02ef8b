"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import { Header as HeaderType } from "@/types/blocks/header";
import Icon from "@/components/icon";
import { Link } from "@/i18n/navigation";
import LocaleToggle from "@/components/locale/toggle";
import { Menu } from "lucide-react";
import SignToggle from "@/components/sign/toggle";
import ThemeToggle from "@/components/theme/toggle";
import { cn } from "@/lib/utils";
import { useEffect, useRef, useState } from "react";

export default function Header({ header }: { header: HeaderType }) {
  const [isScrolled, setIsScrolled] = useState(false);
  // Keep a ref to measure the real header height
  const headerRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Update a CSS variable with the current header height so other sections
  // (e.g. the hero) can subtract it with calc(100vh - var(--header-height)).
  useEffect(() => {
    const updateHeaderHeightVar = () => {
      if (!headerRef.current) return;
      const height = headerRef.current.offsetHeight;
      document.documentElement.style.setProperty(
        "--header-height",
        `${height}px`
      );
    };

    updateHeaderHeightVar();
    window.addEventListener("resize", updateHeaderHeightVar);
    window.addEventListener("scroll", updateHeaderHeightVar, { passive: true });
    return () => {
      window.removeEventListener("resize", updateHeaderHeightVar);
      window.removeEventListener("scroll", updateHeaderHeightVar);
    };
  }, []);

  if (header.disabled) {
    return null;
  }

  return (
    <section
      ref={headerRef}
      className={cn(
        "sticky top-0 z-50 py-3 transition-all duration-200",
        isScrolled && "border-b bg-background/50 backdrop-blur-lg"
      )}
    >
      <div className="container">
        <nav className="hidden justify-between lg:flex">
          <div className="flex items-center gap-6">
            <Link
              href={(header.brand?.url as any) || "/"}
              className="flex items-center gap-2"
            >
              {header.brand?.logo?.src && (
                <img
                  src={header.brand.logo.src}
                  alt={header.brand.logo.alt || header.brand.title}
                  className="w-10"
                />
              )}
              {header.brand?.title && (
                <span className="text-xl text-primary font-bold">
                  {header.brand?.title || ""}
                </span>
              )}
            </Link>
            <div className="flex items-center">
              <NavigationMenu>
                <NavigationMenuList>
                  {header.nav?.items?.map((item, i) => {
                    if (item.children && item.children.length > 0) {
                      return (
                        <NavigationMenuItem
                          key={i}
                          className="text-muted-foreground"
                        >
                          <NavigationMenuTrigger>
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0 mr-2"
                              />
                            )}
                            <span>{item.title}</span>
                          </NavigationMenuTrigger>
                          <NavigationMenuContent>
                            <ul className="w-80 p-3">
                              <NavigationMenuLink>
                                {item.children.map((iitem, ii) => (
                                  <li key={ii}>
                                    <Link
                                      className={cn(
                                        "flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                      )}
                                      href={iitem.url as any}
                                      target={iitem.target}
                                    >
                                      {iitem.icon && (
                                        <Icon
                                          name={iitem.icon}
                                          className="size-5 shrink-0"
                                        />
                                      )}
                                      <div>
                                        <div className="text-sm font-semibold">
                                          {iitem.title}
                                        </div>
                                        <p className="text-sm leading-snug text-muted-foreground">
                                          {iitem.description}
                                        </p>
                                      </div>
                                    </Link>
                                  </li>
                                ))}
                              </NavigationMenuLink>
                            </ul>
                          </NavigationMenuContent>
                        </NavigationMenuItem>
                      );
                    }

                    return (
                      <NavigationMenuItem key={i}>
                        <Link
                          className={cn(
                            navigationMenuTriggerStyle,
                            buttonVariants({
                              variant: "ghost",
                            })
                          )}
                          href={item.url as any}
                          target={item.target}
                        >
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-4 shrink-0 mr-0"
                            />
                          )}
                          {item.title}
                        </Link>
                      </NavigationMenuItem>
                    );
                  })}
                </NavigationMenuList>
              </NavigationMenu>
            </div>
          </div>
          <div className="shrink-0 flex gap-2 items-center">
            {header.show_locale && <LocaleToggle />}
            {header.show_theme && <ThemeToggle />}

            {header.buttons?.map((item, i) => {
              return (
                <Button key={i} variant={item.variant}>
                  <Link
                    href={item.url as any}
                    target={item.target || ""}
                    className="flex items-center gap-1 cursor-pointer"
                  >
                    {item.title}
                    {item.icon && (
                      <Icon name={item.icon} className="size-4 shrink-0" />
                    )}
                  </Link>
                </Button>
              );
            })}
            {header.show_sign && <SignToggle />}
          </div>
        </nav>

        <div className="block lg:hidden">
          <div className="flex items-center justify-between">
            {/* Left: Menu + Logo */}
            <div className="flex items-center gap-3">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Menu className="size-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent className="overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>
                      <Link
                        href={(header.brand?.url || "/") as any}
                        className="flex items-center gap-2"
                      >
                        {header.brand?.logo?.src && (
                          <img
                            src={header.brand.logo.src}
                            alt={header.brand.logo.alt || header.brand.title}
                            className="w-8"
                          />
                        )}
                        {header.brand?.title && (
                          <span className="text-xl font-bold">
                            {header.brand?.title || ""}
                          </span>
                        )}
                      </Link>
                    </SheetTitle>
                  </SheetHeader>
                  <div className="mb-8 mt-8 flex flex-col gap-4 text-primary">
                    <Accordion type="single" collapsible className="w-full">
                      {header.nav?.items?.map((item, i) => {
                        if (item.children && item.children.length > 0) {
                          return (
                            <AccordionItem
                              key={i}
                              value={item.title || ""}
                              className="border-b-0"
                            >
                              <AccordionTrigger className="mb-4 py-0 font-semibold hover:no-underline text-left">
                                {item.title}
                              </AccordionTrigger>
                              <AccordionContent className="mt-2">
                                {item.children.map((iitem, ii) => (
                                  <Link
                                    key={ii}
                                    className={cn(
                                      "flex select-none gap-4 rounded-md p-3 leading-none outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                    )}
                                    href={iitem.url as any}
                                    target={iitem.target}
                                  >
                                    {iitem.icon && (
                                      <Icon
                                        name={iitem.icon}
                                        className="size-4 shrink-0"
                                      />
                                    )}
                                    <div>
                                      <div className="text-sm font-semibold">
                                        {iitem.title}
                                      </div>
                                      <p className="text-sm leading-snug text-muted-foreground">
                                        {iitem.description}
                                      </p>
                                    </div>
                                  </Link>
                                ))}
                              </AccordionContent>
                            </AccordionItem>
                          );
                        }
                        return (
                          <Link
                            key={i}
                            href={item.url as any}
                            target={item.target}
                            className="font-semibold my-4 flex items-center gap-2 px-4"
                          >
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0"
                              />
                            )}
                            {item.title}
                          </Link>
                        );
                      })}
                    </Accordion>
                  </div>
                  <div className="flex-1"></div>
                  <div className="border-t pt-4">
                    <div className="mt-2 flex flex-col gap-3">
                      {header.buttons?.map((item, i) => {
                        return (
                          <Button key={i} variant={item.variant}>
                            <Link
                              href={item.url as any}
                              target={item.target || ""}
                              className="flex items-center gap-1"
                            >
                              {item.title}
                              {item.icon && (
                                <Icon
                                  name={item.icon}
                                  className="size-4 shrink-0"
                                />
                              )}
                            </Link>
                          </Button>
                        );
                      })}

                      {header.show_sign && <SignToggle />}
                    </div>

                    <div className="mt-4 flex items-center gap-2">
                      {header.show_locale && <LocaleToggle />}
                      <div className="flex-1"></div>

                      {header.show_theme && <ThemeToggle />}
                    </div>
                  </div>
                </SheetContent>
              </Sheet>

              <Link
                href={(header.brand?.url || "/") as any}
                className="flex items-center gap-2"
              >
                {header.brand?.logo?.src && (
                  <img
                    src={header.brand.logo.src}
                    alt={header.brand.logo.alt || header.brand.title}
                    className="w-8"
                  />
                )}
                {header.brand?.title && (
                  <span className="text-xl font-bold text-primary">
                    {header.brand?.title || ""}
                  </span>
                )}
              </Link>
            </div>

            {/* Right: Language + Buttons */}
            <div className="flex items-center gap-2">
              {header.show_locale && <LocaleToggle />}

              {header.buttons?.map((item, i) => {
                return (
                  <Button key={i} variant={item.variant} size="sm">
                    <Link
                      href={item.url as any}
                      target={item.target || ""}
                      className="flex items-center gap-1"
                    >
                      {item.title}
                      {item.icon && (
                        <Icon name={item.icon} className="size-4 shrink-0" />
                      )}
                    </Link>
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
