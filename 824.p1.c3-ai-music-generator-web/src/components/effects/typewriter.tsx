"use client";

import { useEffect, useMemo, useRef, useState } from "react";

export interface TypewriterProps {
  texts: string[];
  typingSpeedMs?: number;
  deletingSpeedMs?: number;
  pauseMs?: number;
  className?: string;
}

/**
 * Minimal, dependency-free typewriter effect for React.
 * - Types text character-by-character, then deletes, cycling through provided texts.
 */
export default function Typewriter({
  texts,
  typingSpeedMs = 50,
  deletingSpeedMs = 35,
  pauseMs = 1000,
  className,
}: TypewriterProps) {
  const safeTexts = useMemo(
    () => (Array.isArray(texts) ? texts.filter(Boolean) : []),
    [texts]
  );
  const [textIndex, setTextIndex] = useState(0);
  const [display, setDisplay] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const frameRef = useRef<number | null>(null);
  const lastTickRef = useRef<number>(0);

  useEffect(() => {
    if (safeTexts.length === 0) return;

    const fullText = safeTexts[textIndex % safeTexts.length];

    const step = (now: number) => {
      const interval = isDeleting ? deletingSpeedMs : typingSpeedMs;
      if (now - lastTickRef.current < interval) {
        frameRef.current = requestAnimationFrame(step);
        return;
      }
      lastTickRef.current = now;

      if (!isDeleting) {
        // typing
        const next = fullText.slice(0, display.length + 1);
        setDisplay(next);
        if (next === fullText) {
          // pause at full text, then start deleting
          setTimeout(() => setIsDeleting(true), pauseMs);
        }
      } else {
        // deleting
        const next = fullText.slice(0, Math.max(0, display.length - 1));
        setDisplay(next);
        if (next.length === 0) {
          setIsDeleting(false);
          setTextIndex((i) => (i + 1) % safeTexts.length);
        }
      }

      frameRef.current = requestAnimationFrame(step);
    };

    frameRef.current = requestAnimationFrame(step);
    return () => {
      if (frameRef.current) cancelAnimationFrame(frameRef.current);
    };
  }, [
    safeTexts,
    textIndex,
    isDeleting,
    typingSpeedMs,
    deletingSpeedMs,
    pauseMs,
    display.length,
  ]);

  return (
    <span className={className} aria-live="polite">
      {display}
      <span className="wrap border-r-2 border-white"></span>
    </span>
  );
}
