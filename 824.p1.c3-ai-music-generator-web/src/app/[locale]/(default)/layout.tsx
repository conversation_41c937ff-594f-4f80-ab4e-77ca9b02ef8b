import Policy from "@/components/home/<USER>";
import { ReactNode } from "react";
import Sidenav from "@/components/home/<USER>";
import Sidepanel from "@/components/home/<USER>";
import Signpanel from "@/components/home/<USER>";
import Social from "@/components/home/<USER>";
import Player from "@/components/home/<USER>";
import Locales from "@/components/home/<USER>";
import User from "@/components/home/<USER>";
import Link from "next/link";

export default function HomeLayout({ children }: { children: ReactNode }) {
  return (
    <div className="grid min-h-screen w-full overflow-hidden md:grid-cols-[220px_1fr] lg:grid-cols-[220px_1fr] px-4">
      <div className="hidden border-r border-base-300 md:block">
        <div className="flex h-full max-h-screen fixed flex-col gap-2 w-[220px] lg:w-[220px]">
          <div className="flex h-16 items-center border-none px-4 lg:h-[80px] lg:px-6">
            <Link href="/" className="flex items-center gap-x-2 font-semibold">
              <img src="/logo.png" className="w-10 h-10" />
              <span className="hidden md:block text-2xl font-medium">
                AI Music
              </span>
            </Link>
          </div>
          <div className="flex-1 overflow-y-auto">
            <Sidenav />
          </div>
          <div className="px-4 pb-24 space-y-2">
            <User />
            <Social />
            <Policy />
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <main className="flex-1 overflow-x-hidden overflow-y-auto px-8 py-8 pb-24">
          {children}
        </main>
        <Player />
      </div>
      <Signpanel />
    </div>
  );
}
