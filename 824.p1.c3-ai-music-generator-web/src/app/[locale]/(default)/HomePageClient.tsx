"use client";

import {
  MdLocalFireDepartment,
  MdOutlineRadio,
  MdOutlineRssFeed,
  MdSearch,
  MdOutlinePlayArrow,
  MdStar,
  MdThumbUp,
  MdChatBubbleOutline,
  MdMoreVert,
  MdKeyboardArrowLeft,
  MdKeyboardArrowRight,
  MdCasino,
} from "react-icons/md";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

import { Song } from "@/types/home/<USER>";

interface Banner {
  id: number;
  title: string;
  description: string;
  badge: string;
  badgeColor: string;
  image: string;
  buttonText: string;
}

interface HomePageClientProps {
  trendingSongs: Song[];
  latestSongs: Song[];
  randomSongs: Song[];
  mockBanners: Banner[];
}

export default function HomePageClient({
  trendingSongs,
  latestSongs,
  randomSongs,
  mockBanners,
}: HomePageClientProps) {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % mockBanners.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [mockBanners.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % mockBanners.length);
  };

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + mockBanners.length) % mockBanners.length
    );
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <div className="w-full md:max-w-6xl mx-auto">
      {/* Section 1: Create input and search button */}
      <div className="flex flex-row items-center mb-4 w-full max-md:hidden">
        <div className="flex w-full flex-row items-center gap-3 py-3 px-4 rounded-full bg-red-50 border border-red-200 text-foreground">
          <MdCasino className="h-6 w-6 text-red-500 flex-shrink-0" />
          <div className="relative flex-1 overflow-hidden">
            <Input
              className="w-full bg-inherit border-0 rounded-none px-0 py-2 h-10 text-base placeholder:text-red-400 focus-visible:ring-0 focus-visible:ring-offset-0"
              placeholder="Powerful Pop, mid-tempo, electronic beats, strong bassline, dramatic synths, empowering full female vocals, anthem"
              maxLength={1250}
            />
          </div>
          <Button className="rounded-full bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-2 hover:from-red-600 hover:to-orange-600 flex-shrink-0">
            Create Music
          </Button>
        </div>
      </div>

      {/* Section 2: Auto & Manual Horizontal Carousel */}
      <div className="mb-6">
        <div className="relative flex flex-col overflow-hidden rounded-2xl bg-background group/carousel mb-4">
          <div className="relative w-full flex-1 overflow-x-hidden">
            <div
              className="flex h-full transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {mockBanners.map((banner, index) => (
                <div key={banner.id} className="h-full min-w-0 flex-[0_0_100%]">
                  <div
                    className="overflow-hidden transition-all ease-in-out relative rounded-2xl bg-gradient-to-r from-secondary to-transparent"
                    style={{ height: "358px" }}
                  >
                    <div className="relative flex h-full w-full flex-col">
                      <div className="shrink-0">
                        <div
                          className="absolute inset-0 rounded-2xl bg-cover bg-center transition-opacity duration-500 ease-in-out"
                          style={{
                            backgroundImage: `linear-gradient(to right, rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.2)), url("${banner.image}")`,
                          }}
                        />
                        <div className="relative z-10 flex items-center justify-between gap-4 p-4">
                          <div className="flex min-w-0 flex-1 items-center gap-4 px-2">
                            <div
                              className={`inline-block rounded-full px-2 py-0.5 text-xs font-medium uppercase text-black ${banner.badgeColor}`}
                            >
                              {banner.badge}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="relative flex-1 overflow-hidden">
                        <div className="absolute inset-0 transition-opacity duration-300 opacity-100">
                          <div className="flex flex-col gap-2 rounded-xl p-4 pt-0 md:gap-4 lg:min-h-[286px] lg:flex-row lg:items-end lg:justify-between lg:gap-8 lg:p-6">
                            <div className="relative flex flex-col items-center justify-end gap-2 md:items-start lg:max-w-[400px]">
                              <div className="w-full">
                                <h2 className="mb-4 font-serif text-[36px] leading-[1.2] font-light max-lg:text-center lg:text-[40px] text-white">
                                  {banner.title}
                                </h2>
                                <div className="font-sans text-[14px] leading-[16px] font-normal max-lg:text-center text-gray-400">
                                  <p className="mb-3">{banner.description}</p>
                                </div>
                                <div className="mt-4 flex gap-2 max-lg:justify-center">
                                  <Button className="px-3 text-sm rounded-full bg-white text-black hover:bg-gray-200">
                                    {banner.buttonText}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Navigation dots */}
            <div className="flex gap-2 pt-2 justify-center">
              {mockBanners.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`flex-1 rounded-full transition-all duration-300 h-[2px] max-w-8 ${
                    index === currentSlide
                      ? "bg-white"
                      : "bg-gray-600 hover:bg-gray-400"
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>

            {/* Previous/Next buttons */}
            <div className="absolute top-1/2 flex h-8 w-8 -translate-y-1/2 items-center justify-center transition-[transform,opacity] duration-300 ease-out max-sm:hidden z-10 pointer-events-auto opacity-0 group-hover/carousel:opacity-100 left-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevSlide}
                className="rounded-full bg-white/10 backdrop-blur-lg hover:bg-white/20 text-white border-0"
                aria-label="Previous"
              >
                <MdKeyboardArrowLeft className="h-5 w-5" />
              </Button>
            </div>

            <div className="absolute top-1/2 flex h-8 w-8 -translate-y-1/2 items-center justify-center transition-[transform,opacity] duration-300 ease-out max-sm:hidden z-10 pointer-events-auto opacity-0 group-hover/carousel:opacity-100 right-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={nextSlide}
                className="rounded-full bg-white/10 backdrop-blur-lg hover:bg-white/20 text-white border-0"
                aria-label="Next"
              >
                <MdKeyboardArrowRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Section 3: Three-column layout */}
      <div className="flex flex-col gap-4 mt-4 pb-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* For You Column */}
          <div className="flex min-h-96 flex-col gap-3">
            <div className="flex flex-row justify-between">
              <Link
                href="/for-you"
                className="flex cursor-pointer flex-row items-center hover:underline"
              >
                <div className="font-sans font-semibold text-[20px] leading-[24px] line-clamp-1">
                  For You
                </div>
                <MdOutlineRadio className="h-6 w-6 pt-0.5 text-muted-foreground ml-1" />
              </Link>
            </div>
            <div className="flex flex-col gap-1">
              {trendingSongs?.slice(0, 5).map((song, index) => (
                <SongCard key={song.uuid} song={song} />
              ))}
            </div>
          </div>

          {/* Trending Column */}
          <div className="flex min-h-96 flex-col gap-3">
            <div className="flex flex-row justify-between">
              <Link
                href="/trending"
                className="flex cursor-pointer flex-row items-center hover:underline"
              >
                <div className="font-sans font-semibold text-[20px] leading-[24px] line-clamp-1">
                  Trending
                </div>
                <MdLocalFireDepartment className="h-6 w-6 pt-0.5 text-muted-foreground ml-1" />
              </Link>
            </div>
            <div className="flex flex-col gap-1">
              {trendingSongs?.slice(5, 10).map((song, index) => (
                <SongCard key={song.uuid} song={song} />
              ))}
            </div>
          </div>

          {/* Latest Column */}
          <div className="flex min-h-96 flex-col gap-3">
            <div className="flex flex-row justify-between">
              <Link
                href="/latest"
                className="flex cursor-pointer flex-row items-center hover:underline"
              >
                <div className="font-sans font-semibold text-[20px] leading-[24px] line-clamp-1">
                  Latest
                </div>
                <MdOutlineRssFeed className="h-6 w-6 pt-0.5 text-muted-foreground ml-1" />
              </Link>
            </div>
            <div className="flex flex-col gap-1">
              {latestSongs?.slice(0, 5).map((song, index) => (
                <SongCard key={song.uuid} song={song} />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Section 4: Big card row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8 mb-8">
        {randomSongs?.slice(0, 3).map((song, index) => (
          <Card key={song.uuid} className="overflow-hidden">
            <div className="relative">
              <Image
                src={song.image_url || "/cover.png"}
                alt={song.title || "Untitled"}
                width={400}
                height={200}
                className="w-full h-48 object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                <Button size="lg" className="rounded-full">
                  <MdOutlinePlayArrow className="mr-2 h-6 w-6" />
                  Play
                </Button>
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-semibold text-lg mb-2 truncate">
                {song.title || "Untitled"}
              </h3>
              <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                {song.tags || "No tags"}
              </p>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <MdOutlinePlayArrow className="h-4 w-4" />
                    <span>{Math.floor(Math.random() * 10000)}K</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MdThumbUp className="h-4 w-4" />
                    <span>{Math.floor(Math.random() * 1000)}</span>
                  </div>
                </div>
                <Button variant="ghost" size="icon">
                  <MdMoreVert className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Section 5: Middle card row */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-8">
        {randomSongs?.slice(3, 9).map((song, index) => (
          <Card key={song.uuid} className="overflow-hidden">
            <div className="relative group">
              <Image
                src={song.image_url || "/cover.png"}
                alt={song.title || "Untitled"}
                width={200}
                height={200}
                className="w-full aspect-square object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <Button size="sm" className="rounded-full">
                  <MdOutlinePlayArrow className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="p-3">
              <h4 className="font-medium text-sm mb-1 truncate">
                {song.title || "Untitled"}
              </h4>
              <p className="text-muted-foreground text-xs truncate">
                {song.tags || "No tags"}
              </p>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}

// Song Card Component
function SongCard({ song }: { song: Song }) {
  return (
    <div className="group flex w-full flex-row items-center justify-between rounded-lg p-2 transition-colors duration-150 hover:bg-secondary/50">
      <div className="flex w-full min-w-0 flex-row items-center gap-2">
        <button className="group relative shrink-0" aria-label="Play">
          <Image
            src={song.image_url || "/cover.png"}
            alt={song.title || "Untitled"}
            width={64}
            height={48}
            className="h-16 w-12 rounded-lg object-cover"
          />
          <div className="absolute inset-0 flex items-center justify-center rounded-lg transition-colors duration-150 group-hover:bg-black/30 group-hover:text-white bg-transparent text-transparent">
            <MdOutlinePlayArrow className="h-6 w-6" />
          </div>
        </button>
        <div className="flex flex-col gap-1 min-w-0 flex-1">
          <div className="flex flex-row items-start justify-stretch gap-1 px-1">
            <div className="flex min-w-0 flex-1 flex-col gap-1">
              <div className="flex flex-row items-center justify-start gap-2">
                <Link
                  href={`/song/${song.uuid}`}
                  className="font-sans font-medium text-[16px] leading-[16px] cursor-pointer hover:underline min-w-0 overflow-x-clip text-ellipsis whitespace-nowrap"
                  title={song.title || "Untitled"}
                >
                  {song.title || "Untitled"}
                </Link>
              </div>
              <div className="flex flex-col justify-start gap-1 items-start">
                <div className="gap-2 break-all font-sans font-normal text-[14px] leading-[16px] line-clamp-1 text-muted-foreground">
                  {song.tags || "No tags"}
                </div>
              </div>
            </div>
            <div className="-mt-1 -mr-1 transition-opacity duration-150 group-hover:opacity-100 focus-within:opacity-100 opacity-0">
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <MdMoreVert className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="flex flex-row items-center justify-stretch gap-1 overflow-x-clip px-1">
            <div className="flex w-full flex-row items-center gap-1 font-sans font-normal text-[14px] leading-[16px]">
              <span className="text-muted-foreground">
                {song.artist || "Unknown Artist"}
              </span>
            </div>
            <div className="flex flex-row gap-1">
              <button className="px-1 py-0.5 text-[12px] leading-[14px] rounded-sm bg-transparent hover:bg-secondary text-muted-foreground">
                <span className="relative flex flex-row items-center justify-center gap-0.5">
                  <MdOutlinePlayArrow className="w-3 h-3" />
                  {Math.floor(Math.random() * 10000)}
                </span>
              </button>
              <button className="px-1 py-0.5 text-[12px] leading-[14px] rounded-sm bg-transparent hover:bg-secondary text-muted-foreground">
                <span className="relative flex flex-row items-center justify-center gap-0.5">
                  <MdThumbUp className="w-3 h-3" />
                  {Math.floor(Math.random() * 1000)}
                </span>
              </button>
              <Link
                href={`/song/${song.uuid}?show_comments=true`}
                className="px-1 py-0.5 text-[12px] leading-[14px] rounded-sm bg-transparent hover:bg-secondary text-muted-foreground"
              >
                <span className="relative flex flex-row items-center justify-center gap-0.5">
                  <MdChatBubbleOutline className="w-3 h-3" />
                  {Math.floor(Math.random() * 100)}
                </span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
