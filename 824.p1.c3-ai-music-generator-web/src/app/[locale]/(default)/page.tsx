import {
  MdLocalFireDepartment,
  MdOutlineRadio,
  MdOutlineRssFeed,
  MdSearch,
  MdOutlinePlayArrow,
  MdStar,
  MdThumbUp,
  MdChatBubbleOutline,
  MdMoreVert,
  MdKeyboardArrowLeft,
  MdKeyboardArrowRight,
} from "react-icons/md";
import {
  getLatestMusic,
  getRandomMusic,
  getTrendingMusic,
} from "@/models/music";

import { Metadata } from "next";
import Scroll from "@/components/home/<USER>/scroll";
import { getTranslations } from "next-intl/server";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";
import HomePageClient from "./HomePageClient";
import { Song } from "@/types/home/<USER>";
import { Music } from "@/types/music";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { isAuthEnabled } from "@/lib/auth";

// Convert Music to Song format for compatibility
function convertMusicToSong(music: Music): Song {
  return {
    uuid: music.music_id, // Use music_id as uuid
    video_url: music.video_url,
    audio_url: music.audio_url,
    image_url: music.image_url,
    image_large_url: music.image_large_url,
    llm_model: music.model_name, // Map model_name to llm_model
    tags: music.music_metadata?.tags,
    lyrics: music.music_metadata?.prompt, // Map prompt to lyrics
    description: music.music_metadata?.gpt_description_prompt,
    duration: music.music_metadata?.duration,
    user_uuid: music.user_id,
    title: music.title,
    play_count: music.play_count,
    upvote_count: music.upvote_count,
    created_at: music.created_at,
    status: music.status || "",
    is_public: music.is_public,
    is_trending: false, // Music doesn't have is_trending, default to false
    type: music.music_metadata?.type,
    provider: "suno", // Default provider
    artist: music.display_name,
    prompt: music.music_metadata?.prompt,
  };
}

export const maxDuration = 120;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations("metadata");

  return {
    title: t("discover_title"),
    description: t("discover_description"),
    alternates: {
      canonical: `${process.env.NEXTAUTH_URL}/${locale !== "en" ? locale : ""}`,
    },
  };
}

// Mock data for demo purposes
const mockBanners = [
  {
    id: 1,
    title: "Studio is coming...",
    description:
      "Step into the future of music production — designed to move with your creativity and give you full control. Create stem-by-stem and export to MIDI.",
    badge: "COMING SOON",
    badgeColor: "bg-pink-500",
    image: "https://cdn-o.suno.com/studio-promo-banner-web-6.jpg",
    buttonText: "Join the Waitlist",
  },
  {
    id: 2,
    title: "'Stone' is streaming everywhere",
    description:
      "'Stone' has been officially released to all platforms. Stream it, and listen to the winners of the remix contest.",
    badge: "LISTEN",
    badgeColor: "bg-yellow-500",
    image: "https://cdn-o.suno.com/imoliver-web-banner.jpg",
    buttonText: "Stream",
  },
  {
    id: 3,
    title: "Music that never stops, built live by all of us.",
    description:
      "Experience continuous live music generated by the community. Join thousands of listeners in real-time.",
    badge: "NEW FEATURE",
    badgeColor: "bg-red-500",
    image: "https://suno.com/suno-living-radio-poster.webp",
    buttonText: "Listen Live",
  },
];

// Mock song data to ensure sections display
const mockSongs: Song[] = [
  {
    uuid: "1",
    title: "Midnight Dreams",
    tags: "electronic, ambient, chill",
    image_url:
      "https://cdn2.suno.ai/image_large_23e5e9ad-9ae3-46bc-bfe2-7a9ba0914fb7.jpeg",
    audio_url: "/audio/sample1.mp3",
    artist: "SynthWave Producer",
    status: "complete",
  },
  {
    uuid: "2",
    title: "Neon Nights",
    tags: "synthwave, retro, 80s",
    image_url:
      "https://cdn2.suno.ai/image_large_0d3cbed3-65c3-43b7-b689-8664daa7f0c1.jpeg",
    audio_url: "/audio/sample2.mp3",
    artist: "RetroVibe",
    status: "complete",
  },
  {
    uuid: "3",
    title: "Ocean Waves",
    tags: "ambient, nature, relaxing",
    image_url:
      "https://cdn2.suno.ai/image_large_98e0e7e3-4d66-4ab0-8e55-8e7f5bb3e742.jpeg",
    audio_url: "/audio/sample3.mp3",
    artist: "Nature Sounds",
    status: "complete",
  },
  {
    uuid: "4",
    title: "Urban Pulse",
    tags: "hip hop, urban, beat",
    image_url:
      "https://cdn2.suno.ai/image_large_5a0d8e4b-8103-4d26-bdad-4514d56ea735.jpeg",
    audio_url: "/audio/sample4.mp3",
    artist: "City Beats",
    status: "complete",
  },
  {
    uuid: "5",
    title: "Guitar Melody",
    tags: "acoustic, folk, guitar",
    image_url:
      "https://cdn2.suno.ai/image_large_cf87832a-9f13-42e2-ad3c-8af249ac12cc.jpeg",
    audio_url: "/audio/sample5.mp3",
    artist: "Acoustic Vibes",
    status: "complete",
  },
  {
    uuid: "6",
    title: "Digital Storm",
    tags: "electronic, intense, energy",
    image_url: "https://cdn1.suno.ai/ef04ba2b.webp",
    audio_url: "/audio/sample6.mp3",
    artist: "ElectroStorm",
    status: "complete",
  },
  {
    uuid: "7",
    title: "Sunrise Serenade",
    tags: "classical, piano, peaceful",
    image_url: "https://cdn1.suno.ai/defaultPink.webp",
    audio_url: "/audio/sample7.mp3",
    artist: "Piano Maestro",
    status: "complete",
  },
  {
    uuid: "8",
    title: "Rock Anthem",
    tags: "rock, guitar, powerful",
    image_url: "https://cdn1.suno.ai/defaultOrange.webp",
    audio_url: "/audio/sample8.mp3",
    artist: "Rock Legends",
    status: "complete",
  },
  {
    uuid: "9",
    title: "Jazz Night",
    tags: "jazz, saxophone, smooth",
    image_url:
      "https://cdn2.suno.ai/image_5a0d8e4b-8103-4d26-bdad-4514d56ea735.jpeg",
    audio_url: "/audio/sample9.mp3",
    artist: "Jazz Ensemble",
    status: "complete",
  },
  {
    uuid: "10",
    title: "Future Bass",
    tags: "electronic, bass, modern",
    image_url: "https://cdn1.suno.ai/d243858e.png",
    audio_url: "/audio/sample10.mp3",
    artist: "Bass Master",
    status: "complete",
  },
  {
    uuid: "11",
    title: "Country Road",
    tags: "country, acoustic, storytelling",
    image_url: "/placeholder.png",
    audio_url: "/audio/sample11.mp3",
    artist: "Country Singer",
    status: "complete",
  },
  {
    uuid: "12",
    title: "Techno Beat",
    tags: "techno, dance, club",
    image_url: "/cover.png",
    audio_url: "/audio/sample12.mp3",
    artist: "Techno Producer",
    status: "complete",
  },
];

export default async function () {
  // Check authentication status and redirect if needed
  if (isAuthEnabled()) {
    const session = await auth();
    if (!session) {
      // User is not logged in, redirect to home page
      redirect("/home");
    }
  }

  const t = await getTranslations("nav");

  // Fetch real data
  const trendingMusic = await getTrendingMusic(1, 50);
  const latestMusic = await getLatestMusic(1, 50);
  const randomMusic = await getRandomMusic(1, 50);

  // Convert Music to Song format and use mock data as fallback if real data is empty or null
  const finalTrendingSongs =
    trendingMusic && trendingMusic.length > 0
      ? trendingMusic.map(convertMusicToSong)
      : mockSongs;
  const finalLatestSongs =
    latestMusic && latestMusic.length > 0
      ? latestMusic.map(convertMusicToSong)
      : mockSongs;
  const finalRandomSongs =
    randomMusic && randomMusic.length > 0
      ? randomMusic.map(convertMusicToSong)
      : mockSongs;

  return (
    <HomePageClient
      trendingSongs={finalTrendingSongs}
      latestSongs={finalLatestSongs}
      randomSongs={finalRandomSongs}
      mockBanners={mockBanners}
    />
  );
}
