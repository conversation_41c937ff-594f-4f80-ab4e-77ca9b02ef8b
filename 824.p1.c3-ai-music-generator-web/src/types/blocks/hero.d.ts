import { But<PERSON>, Image, Announcement } from "@/types/blocks/base";

export interface Announcement {
  title?: string;
  description?: string;
  label?: string;
  url?: string;
  target?: string;
}

export interface PromptArea {
  enabled?: boolean;
  placeholder?: string;
  default_value?: string;
  left_icon?: string;
  // New: full button config
  button?: Button & { iconSrc?: string };
}

export interface Hero {
  name?: string;
  disabled?: boolean;
  announcement?: Announcement;
  title?: string;
  highlight_text?: string;
  typewriter_texts?: string[];
  description?: string;
  buttons?: Button[];
  image?: Image;
  tip?: string;
  show_happy_users?: boolean;
  show_badge?: boolean;
  prompt_area?: PromptArea;
}
