import { Header } from "@/types/blocks/header";
import { Hero } from "@/types/blocks/hero";
import { Section } from "@/types/blocks/section";
import { Footer } from "@/types/blocks/footer";
import { Pricing } from "@/types/blocks/pricing";
import { Nav } from "@/types/blocks/nav";
import { Sidenav } from "@/types/blocks/sidenav";
import { Sidepanel } from "@/types/blocks/sidepanel";
import { Signpanel } from "@/types/blocks/signpanel";
import { Social } from "@/types/blocks/social";
import { Player } from "@/types/blocks/player";

export interface LandingPage {
  header?: Header;
  hero?: Hero;
  branding?: Section;
  introduce?: Section;
  benefit?: Section;
  usage?: Section;
  feature?: Section;
  showcase?: Section;
  stats?: Section;
  pricing?: Pricing;
  testimonial?: Section;
  faq?: Section;
  cta?: Section;
  footer?: Footer;
}

export interface PricingPage {
  pricing?: Pricing;
}

export interface ShowcasePage {
  showcase?: Section;
}

export interface HomePage {
  header?: Header;
  nav?: Nav;
  sidenav?: Sidenav;
  sidepanel?: Sidepanel;
  signpanel?: Signpanel;
  social?: Social;
  player?: Player;
  footer?: Footer;
}
