-- 歌曲元数据信息表
CREATE TABLE music_metadatas (
    id SERIAL PRIMARY KEY, -- PK
    music_id UUID UNIQUE NOT NULL, -- 歌曲ID
    user_id UUID NOT NULL, -- 用户ID
    tags VARCHAR(255), -- 标签
    prompt TEXT, -- 提示词(歌词)
    gpt_description_prompt TEXT, -- GPT提示词
    audio_prompt_id VARCHAR(50), -- 音频提示ID
    history TEXT, -- 历史记录
    concat_history JSONB, -- 拼接记录
    type VARCHAR(10), -- 类型
    duration FLOAT, -- 时长
    refund_credits BOOLEAN, -- 退款积分
    stream BOOLEAN, -- 流
    error_type VARCHAR(50), -- 错误类型
    error_message TEXT, -- 错误信息
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

CREATE INDEX idx_music_metadatas_music_id ON music_metadatas(music_id);
CREATE INDEX idx_music_metadatas_user_id ON music_metadatas(user_id);

-- 歌曲信息表
CREATE TABLE musics (
    id SERIAL PRIMARY KEY, -- PK
    music_id UUID UNIQUE NOT NULL, -- 歌曲ID
    user_id UUID NOT NULL, -- 用户ID
    video_url VARCHAR(255), -- 视频链接
    audio_url VARCHAR(255), -- 音频链接
    image_url VARCHAR(255), -- 图片链接
    oss_video_url VARCHAR(255), -- 视频链接
    oss_audio_url VARCHAR(255), -- 音频链接
    oss_image_url VARCHAR(255), -- 图片链接
    image_large_url VARCHAR(255), -- 大图链接
    major_model_version VARCHAR(10), -- 主要模型版本
    model_name VARCHAR(50), -- 模型名称
    is_liked BOOLEAN, -- 是否喜欢
    display_name VARCHAR(50), -- 显示名称
    is_trashed BOOLEAN, -- 是否已删除
    reaction TEXT, -- 反应
    status VARCHAR(20), -- 状态。submitted=已提交 streaming=生成中 complete=已完成 error=错误
    title VARCHAR(100), -- 标题
    play_count INT, -- 播放次数
    upvote_count INT, -- 点赞次数
    is_public BOOLEAN, -- 是否公开
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

ALTER TABLE musics
ADD COLUMN is_retry BOOLEAN; -- 是否需要重试

CREATE INDEX idx_musics_music_id ON musics(music_id);
CREATE INDEX idx_musics_user_id ON musics(user_id);
CREATE INDEX idx_musics_status ON musics(status);
CREATE INDEX idx_musics_is_retry ON musics(is_retry);
