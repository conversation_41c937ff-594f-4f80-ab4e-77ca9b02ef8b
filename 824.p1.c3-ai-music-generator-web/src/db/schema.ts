import {
  pgTable,
  serial,
  varchar,
  text,
  boolean,
  integer,
  timestamp,
  unique,
  uniqueIndex,
  doublePrecision,
  uuid,
  jsonb,
  index,
} from "drizzle-orm/pg-core";

// Users table
export const users = pgTable(
  "users",
  {
    id: integer().primaryKey().generatedAlwaysAsIdentity(),
    uuid: varchar({ length: 255 }).notNull().unique(),
    email: varchar({ length: 255 }).notNull(),
    created_at: timestamp({ withTimezone: true }),
    nickname: varchar({ length: 255 }),
    avatar_url: varchar({ length: 255 }),
    locale: varchar({ length: 50 }),
    signin_type: varchar({ length: 50 }),
    signin_ip: varchar({ length: 255 }),
    signin_provider: varchar({ length: 50 }),
    signin_openid: varchar({ length: 255 }),
    invite_code: varchar({ length: 255 }).notNull().default(""),
    updated_at: timestamp({ withTimezone: true }),
    invited_by: varchar({ length: 255 }).notNull().default(""),
    is_affiliate: boolean().notNull().default(false),
  },
  (table) => [
    uniqueIndex("email_provider_unique_idx").on(
      table.email,
      table.signin_provider
    ),
  ]
);

// Orders table
export const orders = pgTable("orders", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  order_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp({ withTimezone: true }),
  user_uuid: varchar({ length: 255 }).notNull().default(""),
  user_email: varchar({ length: 255 }).notNull().default(""),
  amount: integer().notNull(),
  interval: varchar({ length: 50 }),
  expired_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }).notNull(),
  stripe_session_id: varchar({ length: 255 }),
  credits: integer().notNull(),
  currency: varchar({ length: 50 }),
  sub_id: varchar({ length: 255 }),
  sub_interval_count: integer(),
  sub_cycle_anchor: integer(),
  sub_period_end: integer(),
  sub_period_start: integer(),
  sub_times: integer(),
  product_id: varchar({ length: 255 }),
  product_name: varchar({ length: 255 }),
  valid_months: integer(),
  order_detail: text(),
  paid_at: timestamp({ withTimezone: true }),
  paid_email: varchar({ length: 255 }),
  paid_detail: text(),
});

// API Keys table
export const apikeys = pgTable("apikeys", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  api_key: varchar({ length: 255 }).notNull().unique(),
  title: varchar({ length: 100 }),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
});

// Credits table
export const credits = pgTable("credits", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  trans_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp({ withTimezone: true }),
  user_uuid: varchar({ length: 255 }).notNull(),
  trans_type: varchar({ length: 50 }).notNull(),
  credits: integer().notNull(),
  order_no: varchar({ length: 255 }),
  expired_at: timestamp({ withTimezone: true }),
});

// Posts table
export const posts = pgTable("posts", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  slug: varchar({ length: 255 }),
  title: varchar({ length: 255 }),
  description: text(),
  content: text(),
  created_at: timestamp({ withTimezone: true }),
  updated_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
  cover_url: varchar({ length: 255 }),
  author_name: varchar({ length: 255 }),
  author_avatar_url: varchar({ length: 255 }),
  locale: varchar({ length: 50 }),
});

// Affiliates table
export const affiliates = pgTable("affiliates", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }).notNull().default(""),
  invited_by: varchar({ length: 255 }).notNull(),
  paid_order_no: varchar({ length: 255 }).notNull().default(""),
  paid_amount: integer().notNull().default(0),
  reward_percent: integer().notNull().default(0),
  reward_amount: integer().notNull().default(0),
});

// Feedbacks table
export const feedbacks = pgTable("feedbacks", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
  user_uuid: varchar({ length: 255 }),
  content: text(),
  rating: integer(),
});

// Songs table
export const songs = pgTable("songs", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  video_url: varchar({ length: 255 }),
  audio_url: varchar({ length: 255 }),
  image_url: varchar({ length: 255 }),
  image_large_url: varchar({ length: 255 }),
  llm_model: varchar({ length: 50 }),
  tags: varchar({ length: 255 }),
  lyrics: text(),
  description: text(),
  duration: doublePrecision(),
  type: varchar({ length: 50 }),
  user_uuid: varchar({ length: 255 }),
  title: varchar({ length: 255 }),
  play_count: integer(),
  upvote_count: integer(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
  is_public: boolean(),
  is_trending: boolean(),
  provider: varchar({ length: 50 }),
  artist: varchar({ length: 100 }),
  prompt: text(),
});

// Music Metadatas table
export const musicMetadatas = pgTable(
  "music_metadatas",
  {
    id: serial("id").primaryKey(),
    music_id: varchar("music_id", { length: 255 }).notNull().unique(),
    user_id: varchar("user_id", { length: 255 }).notNull(),
    tags: varchar("tags", { length: 255 }),
    prompt: text("prompt"),
    gpt_description_prompt: text("gpt_description_prompt"),
    audio_prompt_id: varchar("audio_prompt_id", { length: 50 }),
    history: text("history"),
    concat_history: jsonb("concat_history"),
    type: varchar("type", { length: 10 }),
    duration: doublePrecision("duration"),
    refund_credits: boolean("refund_credits"),
    stream: boolean("stream"),
    error_type: varchar("error_type", { length: 50 }),
    error_message: text("error_message"),
    created_at: timestamp("created_at", { withTimezone: true }),
    updated_at: timestamp("updated_at", { withTimezone: true }),
  },
  (table) => [
    index("idx_music_metadatas_music_id").on(table.music_id),
    index("idx_music_metadatas_user_id").on(table.user_id),
  ]
);

// Musics table
export const musics = pgTable(
  "musics",
  {
    id: serial("id").primaryKey(),
    music_id: varchar("music_id", { length: 255 }).notNull().unique(),
    user_id: varchar("user_id", { length: 255 }).notNull(),
    video_url: varchar("video_url", { length: 255 }),
    audio_url: varchar("audio_url", { length: 255 }),
    image_url: varchar("image_url", { length: 255 }),
    oss_video_url: varchar("oss_video_url", { length: 255 }),
    oss_audio_url: varchar("oss_audio_url", { length: 255 }),
    oss_image_url: varchar("oss_image_url", { length: 255 }),
    image_large_url: varchar("image_large_url", { length: 255 }),
    major_model_version: varchar("major_model_version", { length: 10 }),
    model_name: varchar("model_name", { length: 50 }),
    is_liked: boolean("is_liked"),
    display_name: varchar("display_name", { length: 50 }),
    is_trashed: boolean("is_trashed"),
    reaction: text("reaction"),
    status: varchar("status", { length: 20 }),
    title: varchar("title", { length: 100 }),
    play_count: integer("play_count"),
    upvote_count: integer("upvote_count"),
    is_public: boolean("is_public"),
    is_retry: boolean("is_retry"),
    created_at: timestamp("created_at", { withTimezone: true }),
    updated_at: timestamp("updated_at", { withTimezone: true }),
  },
  (table) => [
    index("idx_musics_music_id").on(table.music_id),
    index("idx_musics_user_id").on(table.user_id),
    index("idx_musics_status").on(table.status),
    index("idx_musics_is_retry").on(table.is_retry),
  ]
);
