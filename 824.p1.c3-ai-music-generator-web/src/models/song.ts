import { songs } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, sql } from "drizzle-orm";
import type { Song } from "@/types/home/<USER>";

type DbSong = typeof songs.$inferSelect;

function mapDbSongToUiSong(row: DbSong): Song {
  return {
    uuid: row.uuid,
    video_url: row.video_url ?? undefined,
    audio_url: row.audio_url ?? undefined,
    image_url: row.image_url ?? undefined,
    image_large_url: row.image_large_url ?? undefined,
    llm_model: row.llm_model ?? undefined,
    tags: row.tags ?? undefined,
    lyrics: row.lyrics ?? undefined,
    description: row.description ?? undefined,
    duration: row.duration ?? undefined,
    type: row.type ?? undefined,
    user_uuid: row.user_uuid ?? undefined,
    title: row.title ?? undefined,
    play_count: row.play_count ?? undefined,
    upvote_count: row.upvote_count ?? undefined,
    created_at: row.created_at ? row.created_at.toISOString() : undefined,
    status: row.status ?? "",
    is_public: row.is_public ?? undefined,
    is_trending: row.is_trending ?? undefined,
    provider: row.provider ?? undefined,
    artist: row.artist ?? undefined,
    prompt: row.prompt ?? undefined,
  };
}

export async function getLatestSongs(
  page: number = 1,
  limit: number = 50
): Promise<Song[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(songs)
    .where(eq(songs.is_public, true))
    .orderBy(desc(songs.created_at))
    .limit(limit)
    .offset(offset);

  return data.map(mapDbSongToUiSong);
}

export async function getTrendingSongs(
  page: number = 1,
  limit: number = 50
): Promise<Song[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(songs)
    .where(eq(songs.is_public, true))
    .orderBy(
      desc(songs.is_trending),
      desc(songs.play_count),
      desc(songs.upvote_count),
      desc(songs.created_at)
    )
    .limit(limit)
    .offset(offset);

  return data.map(mapDbSongToUiSong);
}

export async function getRandomSongs(
  page: number = 1,
  limit: number = 50
): Promise<Song[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(songs)
    .where(eq(songs.is_public, true))
    .orderBy(sql`random()`)
    .limit(limit)
    .offset(offset);

  return data.map(mapDbSongToUiSong);
}
