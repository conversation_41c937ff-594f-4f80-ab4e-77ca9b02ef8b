import { musics, musicMetadatas } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, sql, and } from "drizzle-orm";
import type { Music, MusicMetadata } from "@/types/music";

type DbMusic = typeof musics.$inferSelect;
type DbMusicMetadata = typeof musicMetadatas.$inferSelect;

// Combined type for joined query results
type DbMusicWithMetadata = DbMusic & {
  metadata_tags?: string | null;
  metadata_prompt?: string | null;
  metadata_gpt_description_prompt?: string | null;
  metadata_audio_prompt_id?: string | null;
  metadata_history?: string | null;
  metadata_concat_history?: any | null;
  metadata_type?: string | null;
  metadata_duration?: number | null;
  metadata_refund_credits?: boolean | null;
  metadata_stream?: boolean | null;
  metadata_error_type?: string | null;
  metadata_error_message?: string | null;
  metadata_created_at?: Date | null;
  metadata_updated_at?: Date | null;
};

function mapDbMusicToUiMusic(row: DbMusicWithMetadata): Music {
  const music: Music = {
    id: row.id,
    music_id: row.music_id,
    user_id: row.user_id,
    video_url: row.video_url ?? undefined,
    audio_url: row.audio_url ?? undefined,
    image_url: row.image_url ?? undefined,
    oss_video_url: row.oss_video_url ?? undefined,
    oss_audio_url: row.oss_audio_url ?? undefined,
    oss_image_url: row.oss_image_url ?? undefined,
    image_large_url: row.image_large_url ?? undefined,
    major_model_version: row.major_model_version ?? undefined,
    model_name: row.model_name ?? undefined,
    is_liked: row.is_liked ?? undefined,
    display_name: row.display_name ?? undefined,
    is_trashed: row.is_trashed ?? undefined,
    reaction: row.reaction ?? undefined,
    status: row.status ?? undefined,
    title: row.title ?? undefined,
    play_count: row.play_count ?? undefined,
    upvote_count: row.upvote_count ?? undefined,
    is_public: row.is_public ?? undefined,
    is_retry: row.is_retry ?? undefined,
    created_at: row.created_at ? row.created_at.toISOString() : undefined,
    updated_at: row.updated_at ? row.updated_at.toISOString() : undefined,
  };

  // Add metadata if available
  if (row.metadata_tags !== undefined || row.metadata_prompt !== undefined) {
    music.music_metadata = {
      music_id: row.music_id,
      user_id: row.user_id,
      tags: row.metadata_tags ?? undefined,
      prompt: row.metadata_prompt ?? undefined,
      gpt_description_prompt: row.metadata_gpt_description_prompt ?? undefined,
      audio_prompt_id: row.metadata_audio_prompt_id ?? undefined,
      history: row.metadata_history ?? undefined,
      concat_history: row.metadata_concat_history ?? undefined,
      type: row.metadata_type ?? undefined,
      duration: row.metadata_duration ?? undefined,
      refund_credits: row.metadata_refund_credits ?? undefined,
      stream: row.metadata_stream ?? undefined,
      error_type: row.metadata_error_type ?? undefined,
      error_message: row.metadata_error_message ?? undefined,
      created_at: row.metadata_created_at
        ? row.metadata_created_at.toISOString()
        : undefined,
      updated_at: row.metadata_updated_at
        ? row.metadata_updated_at.toISOString()
        : undefined,
    };
  }

  return music;
}

/**
 * Get latest music with pagination
 */
export async function getLatestMusic(
  page: number = 1,
  limit: number = 50
): Promise<Music[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select({
      // Music fields
      id: musics.id,
      music_id: musics.music_id,
      user_id: musics.user_id,
      video_url: musics.video_url,
      audio_url: musics.audio_url,
      image_url: musics.image_url,
      oss_video_url: musics.oss_video_url,
      oss_audio_url: musics.oss_audio_url,
      oss_image_url: musics.oss_image_url,
      image_large_url: musics.image_large_url,
      major_model_version: musics.major_model_version,
      model_name: musics.model_name,
      is_liked: musics.is_liked,
      display_name: musics.display_name,
      is_trashed: musics.is_trashed,
      reaction: musics.reaction,
      status: musics.status,
      title: musics.title,
      play_count: musics.play_count,
      upvote_count: musics.upvote_count,
      is_public: musics.is_public,
      is_retry: musics.is_retry,
      created_at: musics.created_at,
      updated_at: musics.updated_at,
      // Metadata fields with aliases
      metadata_tags: musicMetadatas.tags,
      metadata_prompt: musicMetadatas.prompt,
      metadata_gpt_description_prompt: musicMetadatas.gpt_description_prompt,
      metadata_audio_prompt_id: musicMetadatas.audio_prompt_id,
      metadata_history: musicMetadatas.history,
      metadata_concat_history: musicMetadatas.concat_history,
      metadata_type: musicMetadatas.type,
      metadata_duration: musicMetadatas.duration,
      metadata_refund_credits: musicMetadatas.refund_credits,
      metadata_stream: musicMetadatas.stream,
      metadata_error_type: musicMetadatas.error_type,
      metadata_error_message: musicMetadatas.error_message,
      metadata_created_at: musicMetadatas.created_at,
      metadata_updated_at: musicMetadatas.updated_at,
    })
    .from(musics)
    .leftJoin(musicMetadatas, eq(musics.music_id, musicMetadatas.music_id))
    .where(and(eq(musics.is_public, true), eq(musics.is_trashed, false)))
    .orderBy(desc(musics.created_at))
    .limit(limit)
    .offset(offset);

  return data.map(mapDbMusicToUiMusic);
}

/**
 * Get trending music with pagination
 */
export async function getTrendingMusic(
  page: number = 1,
  limit: number = 50
): Promise<Music[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select({
      // Music fields
      id: musics.id,
      music_id: musics.music_id,
      user_id: musics.user_id,
      video_url: musics.video_url,
      audio_url: musics.audio_url,
      image_url: musics.image_url,
      oss_video_url: musics.oss_video_url,
      oss_audio_url: musics.oss_audio_url,
      oss_image_url: musics.oss_image_url,
      image_large_url: musics.image_large_url,
      major_model_version: musics.major_model_version,
      model_name: musics.model_name,
      is_liked: musics.is_liked,
      display_name: musics.display_name,
      is_trashed: musics.is_trashed,
      reaction: musics.reaction,
      status: musics.status,
      title: musics.title,
      play_count: musics.play_count,
      upvote_count: musics.upvote_count,
      is_public: musics.is_public,
      is_retry: musics.is_retry,
      created_at: musics.created_at,
      updated_at: musics.updated_at,
      // Metadata fields with aliases
      metadata_tags: musicMetadatas.tags,
      metadata_prompt: musicMetadatas.prompt,
      metadata_gpt_description_prompt: musicMetadatas.gpt_description_prompt,
      metadata_audio_prompt_id: musicMetadatas.audio_prompt_id,
      metadata_history: musicMetadatas.history,
      metadata_concat_history: musicMetadatas.concat_history,
      metadata_type: musicMetadatas.type,
      metadata_duration: musicMetadatas.duration,
      metadata_refund_credits: musicMetadatas.refund_credits,
      metadata_stream: musicMetadatas.stream,
      metadata_error_type: musicMetadatas.error_type,
      metadata_error_message: musicMetadatas.error_message,
      metadata_created_at: musicMetadatas.created_at,
      metadata_updated_at: musicMetadatas.updated_at,
    })
    .from(musics)
    .leftJoin(musicMetadatas, eq(musics.music_id, musicMetadatas.music_id))
    .where(and(eq(musics.is_public, true), eq(musics.is_trashed, false)))
    .orderBy(
      desc(musics.play_count),
      desc(musics.upvote_count),
      desc(musics.created_at)
    )
    .limit(limit)
    .offset(offset);

  return data.map(mapDbMusicToUiMusic);
}

/**
 * Get random music with pagination
 */
export async function getRandomMusic(
  page: number = 1,
  limit: number = 50
): Promise<Music[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select({
      // Music fields
      id: musics.id,
      music_id: musics.music_id,
      user_id: musics.user_id,
      video_url: musics.video_url,
      audio_url: musics.audio_url,
      image_url: musics.image_url,
      oss_video_url: musics.oss_video_url,
      oss_audio_url: musics.oss_audio_url,
      oss_image_url: musics.oss_image_url,
      image_large_url: musics.image_large_url,
      major_model_version: musics.major_model_version,
      model_name: musics.model_name,
      is_liked: musics.is_liked,
      display_name: musics.display_name,
      is_trashed: musics.is_trashed,
      reaction: musics.reaction,
      status: musics.status,
      title: musics.title,
      play_count: musics.play_count,
      upvote_count: musics.upvote_count,
      is_public: musics.is_public,
      is_retry: musics.is_retry,
      created_at: musics.created_at,
      updated_at: musics.updated_at,
      // Metadata fields with aliases
      metadata_tags: musicMetadatas.tags,
      metadata_prompt: musicMetadatas.prompt,
      metadata_gpt_description_prompt: musicMetadatas.gpt_description_prompt,
      metadata_audio_prompt_id: musicMetadatas.audio_prompt_id,
      metadata_history: musicMetadatas.history,
      metadata_concat_history: musicMetadatas.concat_history,
      metadata_type: musicMetadatas.type,
      metadata_duration: musicMetadatas.duration,
      metadata_refund_credits: musicMetadatas.refund_credits,
      metadata_stream: musicMetadatas.stream,
      metadata_error_type: musicMetadatas.error_type,
      metadata_error_message: musicMetadatas.error_message,
      metadata_created_at: musicMetadatas.created_at,
      metadata_updated_at: musicMetadatas.updated_at,
    })
    .from(musics)
    .leftJoin(musicMetadatas, eq(musics.music_id, musicMetadatas.music_id))
    .where(and(eq(musics.is_public, true), eq(musics.is_trashed, false)))
    .orderBy(sql`random()`)
    .limit(limit)
    .offset(offset);

  return data.map(mapDbMusicToUiMusic);
}

/**
 * Get music by user ID with pagination
 */
export async function getMusicByUser(
  userId: string,
  page: number = 1,
  limit: number = 50
): Promise<Music[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select({
      // Music fields
      id: musics.id,
      music_id: musics.music_id,
      user_id: musics.user_id,
      video_url: musics.video_url,
      audio_url: musics.audio_url,
      image_url: musics.image_url,
      oss_video_url: musics.oss_video_url,
      oss_audio_url: musics.oss_audio_url,
      oss_image_url: musics.oss_image_url,
      image_large_url: musics.image_large_url,
      major_model_version: musics.major_model_version,
      model_name: musics.model_name,
      is_liked: musics.is_liked,
      display_name: musics.display_name,
      is_trashed: musics.is_trashed,
      reaction: musics.reaction,
      status: musics.status,
      title: musics.title,
      play_count: musics.play_count,
      upvote_count: musics.upvote_count,
      is_public: musics.is_public,
      is_retry: musics.is_retry,
      created_at: musics.created_at,
      updated_at: musics.updated_at,
      // Metadata fields with aliases
      metadata_tags: musicMetadatas.tags,
      metadata_prompt: musicMetadatas.prompt,
      metadata_gpt_description_prompt: musicMetadatas.gpt_description_prompt,
      metadata_audio_prompt_id: musicMetadatas.audio_prompt_id,
      metadata_history: musicMetadatas.history,
      metadata_concat_history: musicMetadatas.concat_history,
      metadata_type: musicMetadatas.type,
      metadata_duration: musicMetadatas.duration,
      metadata_refund_credits: musicMetadatas.refund_credits,
      metadata_stream: musicMetadatas.stream,
      metadata_error_type: musicMetadatas.error_type,
      metadata_error_message: musicMetadatas.error_message,
      metadata_created_at: musicMetadatas.created_at,
      metadata_updated_at: musicMetadatas.updated_at,
    })
    .from(musics)
    .leftJoin(musicMetadatas, eq(musics.music_id, musicMetadatas.music_id))
    .where(and(eq(musics.user_id, userId), eq(musics.is_trashed, false)))
    .orderBy(desc(musics.created_at))
    .limit(limit)
    .offset(offset);

  return data.map(mapDbMusicToUiMusic);
}

/**
 * Get music by music ID
 */
export async function getMusicById(
  musicId: string
): Promise<Music | undefined> {
  const data = await db()
    .select({
      // Music fields
      id: musics.id,
      music_id: musics.music_id,
      user_id: musics.user_id,
      video_url: musics.video_url,
      audio_url: musics.audio_url,
      image_url: musics.image_url,
      oss_video_url: musics.oss_video_url,
      oss_audio_url: musics.oss_audio_url,
      oss_image_url: musics.oss_image_url,
      image_large_url: musics.image_large_url,
      major_model_version: musics.major_model_version,
      model_name: musics.model_name,
      is_liked: musics.is_liked,
      display_name: musics.display_name,
      is_trashed: musics.is_trashed,
      reaction: musics.reaction,
      status: musics.status,
      title: musics.title,
      play_count: musics.play_count,
      upvote_count: musics.upvote_count,
      is_public: musics.is_public,
      is_retry: musics.is_retry,
      created_at: musics.created_at,
      updated_at: musics.updated_at,
      // Metadata fields with aliases
      metadata_tags: musicMetadatas.tags,
      metadata_prompt: musicMetadatas.prompt,
      metadata_gpt_description_prompt: musicMetadatas.gpt_description_prompt,
      metadata_audio_prompt_id: musicMetadatas.audio_prompt_id,
      metadata_history: musicMetadatas.history,
      metadata_concat_history: musicMetadatas.concat_history,
      metadata_type: musicMetadatas.type,
      metadata_duration: musicMetadatas.duration,
      metadata_refund_credits: musicMetadatas.refund_credits,
      metadata_stream: musicMetadatas.stream,
      metadata_error_type: musicMetadatas.error_type,
      metadata_error_message: musicMetadatas.error_message,
      metadata_created_at: musicMetadatas.created_at,
      metadata_updated_at: musicMetadatas.updated_at,
    })
    .from(musics)
    .leftJoin(musicMetadatas, eq(musics.music_id, musicMetadatas.music_id))
    .where(eq(musics.music_id, musicId))
    .limit(1);

  if (data.length === 0) {
    return undefined;
  }

  return mapDbMusicToUiMusic(data[0]);
}

/**
 * Get music by status with pagination
 */
export async function getMusicByStatus(
  status: string,
  page: number = 1,
  limit: number = 50
): Promise<Music[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select({
      // Music fields
      id: musics.id,
      music_id: musics.music_id,
      user_id: musics.user_id,
      video_url: musics.video_url,
      audio_url: musics.audio_url,
      image_url: musics.image_url,
      oss_video_url: musics.oss_video_url,
      oss_audio_url: musics.oss_audio_url,
      oss_image_url: musics.oss_image_url,
      image_large_url: musics.image_large_url,
      major_model_version: musics.major_model_version,
      model_name: musics.model_name,
      is_liked: musics.is_liked,
      display_name: musics.display_name,
      is_trashed: musics.is_trashed,
      reaction: musics.reaction,
      status: musics.status,
      title: musics.title,
      play_count: musics.play_count,
      upvote_count: musics.upvote_count,
      is_public: musics.is_public,
      is_retry: musics.is_retry,
      created_at: musics.created_at,
      updated_at: musics.updated_at,
      // Metadata fields with aliases
      metadata_tags: musicMetadatas.tags,
      metadata_prompt: musicMetadatas.prompt,
      metadata_gpt_description_prompt: musicMetadatas.gpt_description_prompt,
      metadata_audio_prompt_id: musicMetadatas.audio_prompt_id,
      metadata_history: musicMetadatas.history,
      metadata_concat_history: musicMetadatas.concat_history,
      metadata_type: musicMetadatas.type,
      metadata_duration: musicMetadatas.duration,
      metadata_refund_credits: musicMetadatas.refund_credits,
      metadata_stream: musicMetadatas.stream,
      metadata_error_type: musicMetadatas.error_type,
      metadata_error_message: musicMetadatas.error_message,
      metadata_created_at: musicMetadatas.created_at,
      metadata_updated_at: musicMetadatas.updated_at,
    })
    .from(musics)
    .leftJoin(musicMetadatas, eq(musics.music_id, musicMetadatas.music_id))
    .where(and(eq(musics.status, status), eq(musics.is_trashed, false)))
    .orderBy(desc(musics.created_at))
    .limit(limit)
    .offset(offset);

  return data.map(mapDbMusicToUiMusic);
}
